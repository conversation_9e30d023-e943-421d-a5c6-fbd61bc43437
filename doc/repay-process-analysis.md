# 还款流程分析文档

## 概述
本文档详细分析了现金贷系统的还款流程，包括线上还款和线下还款的完整过程，以及各个表的状态变化。

## 核心表结构

### 1. CustomRepayRecord (对客还款记录表)
- **作用**: 记录用户发起的还款请求和处理结果
- **关键字段**:
  - `repayState`: 还款状态 (INIT → PROCESSING → SUCCEED/FAILED)
  - `repayMode`: 还款模式 (ONLINE/OFFLINE)
  - `repayPurpose`: 还款目的 (CURRENT/CLEAR)
  - `totalAmt`: 还款总金额
  - `reduceAmount`: 减免金额

### 2. BankRepayRecord (对资还款记录表)
- **作用**: 记录向资方发起的还款请求和结果
- **关键字段**:
  - `state`: 处理状态 (INIT → PROCESSING → SUCCEED/FAILED)
  - `sourceRecordId`: 关联的对客还款记录ID
  - `bankRepayNo`: 资方返回的还款流水号

### 3. RepayPlan (还款计划表)
- **作用**: 记录每期的还款计划和实际还款情况
- **关键字段**:
  - `custRepayState`: 客户还款状态 (NORMAL → REPAID)
  - `actRepayTime`: 实际还款时间
  - `actAmount`: 实际还款金额

### 4. Loan (借据表)
- **作用**: 记录放款信息和状态
- **关键字段**:
  - `loanState`: 放款状态
  - `flowChannel`: 流量渠道
  - `bankChannel`: 资金渠道

## 还款流程详解

### 线上还款流程

#### 1. 还款申请阶段
```
用户发起还款请求 → RepayService.online()
```

**状态变化**:
- 创建 `CustomRepayRecord` 记录，状态为 `INIT`
- 进行还款试算和校验

**主要步骤**:
1. 系统维护时间校验
2. 获取放款信息和还款计划
3. 加锁防止重复提交 (`cash_business_repay_{loanId}`)
4. 校验还款记录是否存在处理中的订单
5. 初始化对客还款记录 (`CustomRepayRecord`)
6. 还款时间段校验
7. 还款试算 (`TrialService.repayTrial()`)
8. 减免金额校验 (仅LVXIN渠道)

#### 2. 扣款申请阶段
```
试算成功 → 创建BankRepayRecord → 发送MQ消息
```

**状态变化**:
- 创建 `BankRepayRecord` 记录，状态为 `INIT`
- 发送 `submitRepay(bankRepayRecordId)` 消息到MQ

#### 3. 资方扣款阶段
```
MQ消息处理 → RepayService.repay() → 调用资方接口
```

**状态变化**:
- `CustomRepayRecord.repayState`: INIT → PROCESSING
- `BankRepayRecord.state`: INIT → PROCESSING (成功时)
- 设置 `bankRepayNo` (资方流水号)

**主要步骤**:
1. 获取用户还款银行卡信息
2. 构建还款申请参数 (`RepayApplyDto`)
3. 调用 `finRepayService.repay()` 通知资方扣款
4. 发送延迟查询消息 `submitRepayQueryDelay()`

#### 4. 结果查询阶段
```
延迟MQ消息 → RepayService.repayResult() → 查询资方结果
```

**成功时状态变化**:
- `BankRepayRecord.state`: PROCESSING → SUCCEED
- `RepayPlan.custRepayState`: NORMAL → REPAID
- `RepayPlan.actRepayTime`: 设置实际还款时间
- `RepayPlan.actAmount`: 设置实际还款金额

**失败时状态变化**:
- `BankRepayRecord.state`: PROCESSING → FAILED
- `CustomRepayRecord.repayState`: PROCESSING → FAILED
- 设置失败原因 `failReason`

#### 5. 成功后处理
```
资方成功 → 发布PrincipalRepaySucceedEvent → 更新状态 → 发布RepaySucceedResultEvent
```

**事件处理流程**:
1. **PrincipalRepaySucceedEvent**:
   - `CustomRepayRecord.repayState`: PROCESSING → SUCCEED
   - `RepayPlan.custRepayState`: NORMAL → REPAID
   - 如果是结清还款，将后续期次也标记为 REPAID

2. **RepaySucceedResultEvent**:
   - 发送还款成功回调
   - 如果是结清或最后一期，更新订单状态为 CLEAR
   - 发送结清回调

### 线下还款流程

#### 1. 线下销账申请
```
管理端发起 → RepayService.offline()
```

**主要差异**:
- 支持减免申请的使用
- 可以指定还款时间
- 需要人工审核通过

**状态变化**:
- 创建 `OfflineRepayApply` 记录
- 创建 `CustomRepayRecord` 和 `BankRepayRecord`
- 后续流程与线上还款相同

## 状态枚举说明

### ProcessState (处理状态)
- `INIT`: 初始状态
- `PROCESSING`: 处理中
- `SUCCEED`: 成功 (终态)
- `FAILED`: 失败 (终态)

### RepayState (还款状态)
- `NORMAL`: 正常未还
- `REPAID`: 已还款

### RepayPurpose (还款目的)
- `CURRENT`: 当期还款
- `CLEAR`: 提前结清

### RepayMode (还款模式)
- `ONLINE`: 线上还款
- `OFFLINE`: 线下还款

## 消息队列处理

### 还款相关队列
1. **REPAY_APPLY**: 还款申请队列
   - 处理器: `RepayListener.listenRepayApply()`
   - 调用: `RepayService.repay()`

2. **REPAY_QUERY_DELAY**: 还款结果查询延迟队列
   - 处理器: `RepayResultListener.listenRepayResult()`
   - 调用: `RepayService.repayResult()`

3. **CALLBACK_COMMON_NOTIFY**: 回调通知队列
   - 用于向流量方发送还款结果通知

## 异常处理机制

### 1. 重复提交防护
- 使用分布式锁 `cash_business_repay_{loanId}`
- 锁定时间: 20秒

### 2. 异常重试
- 资方调用异常时，仍会发送延迟查询消息
- 通过MQ延迟消息实现自动重试

### 3. 状态一致性检查
- 宝付与资方状态不一致时发出预警
- 继续延迟查询直到状态一致

## 关键业务规则

### 1. 减免规则 (仅LVXIN渠道)
- 逾期且非宽限期内才能减免
- 罚息减免不能超过平台收取部分 (对客罚息-对资罚息)
- 咨询费可全额减免

### 2. 宽限期计算
- 不同银行渠道有不同宽限期
- HXBK在年结期间会增加宽限期天数

### 3. 结清处理
- 结清时会将后续所有期次标记为已还
- 更新订单状态为结清
- 发送结清回调通知

## 监控告警

### 1. 状态不一致告警
- 宝付与资方状态不匹配
- 还款计划状态异常

### 2. 业务异常告警
- 还款申请失败
- 资方接口调用异常
- 状态更新失败

## 详细状态变化时序

### 线上还款成功场景
```
时间点 | CustomRepayRecord | BankRepayRecord | RepayPlan | 操作
-----|------------------|-----------------|-----------|-----
T1   | INIT             | -               | NORMAL    | 创建对客记录
T2   | INIT             | INIT            | NORMAL    | 创建对资记录，发送MQ
T3   | PROCESSING       | PROCESSING      | NORMAL    | 资方扣款中
T4   | PROCESSING       | SUCCEED         | NORMAL    | 资方扣款成功
T5   | SUCCEED          | SUCCEED         | REPAID    | 事件处理完成
```

### 线上还款失败场景
```
时间点 | CustomRepayRecord | BankRepayRecord | RepayPlan | 操作
-----|------------------|-----------------|-----------|-----
T1   | INIT             | -               | NORMAL    | 创建对客记录
T2   | INIT             | INIT            | NORMAL    | 创建对资记录，发送MQ
T3   | PROCESSING       | PROCESSING      | NORMAL    | 资方扣款中
T4   | FAILED           | FAILED          | NORMAL    | 资方扣款失败
```

### 提前结清成功场景
```
时间点 | CustomRepayRecord | BankRepayRecord | RepayPlan(当期) | RepayPlan(后续期) | Order
-----|------------------|-----------------|----------------|------------------|-------
T1   | INIT             | -               | NORMAL         | NORMAL           | LOAN_PASS
T2   | INIT             | INIT            | NORMAL         | NORMAL           | LOAN_PASS
T3   | PROCESSING       | PROCESSING      | NORMAL         | NORMAL           | LOAN_PASS
T4   | PROCESSING       | SUCCEED         | NORMAL         | NORMAL           | LOAN_PASS
T5   | SUCCEED          | SUCCEED         | REPAID         | REPAID           | CLEAR
```

## 关键代码位置

### 1. 还款入口
- **线上还款**: `RepayService.online(OnlineRepayRequestDto)`
- **线下还款**: `RepayService.offline(OfflineRepayApplyRequest)`

### 2. 状态更新关键方法
- **资方成功处理**: `RepayService.capitalSuccessUpdate()`
- **失败处理**: `RepayService.failUpdate()`
- **事件监听**: `PrincipalRepaySucceedListener.online()`

### 3. MQ消息处理
- **还款申请**: `RepayListener.listenRepayApply()`
- **结果查询**: `RepayResultListener.listenRepayResult()`

## 数据库表关系

```
Loan (借据)
  ├── RepayPlan (还款计划) [1:N]
  └── CustomRepayRecord (对客还款记录) [1:N]
       └── BankRepayRecord (对资还款记录) [1:1]

Order (订单)
  └── Loan (借据) [1:1]
```

## 总结

还款流程是一个复杂的异步处理过程，涉及多个系统的交互：
1. **Flow系统**: 处理用户还款请求，管理还款状态
2. **Capital系统**: 与资方对接，处理实际扣款
3. **支付系统**: 处理用户银行卡扣款
4. **MQ系统**: 保证异步处理的可靠性

整个流程通过状态机模式管理，确保数据一致性和业务完整性。关键的设计原则包括：
- **幂等性**: 通过分布式锁和状态检查防止重复处理
- **可靠性**: 通过MQ延迟消息实现自动重试
- **一致性**: 通过事件驱动模式保证多表状态同步
- **可观测性**: 通过日志和告警及时发现异常
